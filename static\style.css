* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
}

header h1 {
    color: white;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.control-panel {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.status-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: inline-block;
    animation: pulse 2s infinite;
}

.status-dot.stopped {
    background-color: #dc3545;
}

.status-dot.starting {
    background-color: #ffc107;
}

.status-dot.running {
    background-color: #28a745;
}

.status-dot.complete {
    background-color: #007bff;
    box-shadow: 0 0 10px rgba(0,123,255,0.5);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

#status-text {
    font-weight: bold;
    font-size: 1.1em;
}

.startup-status {
    font-style: italic;
    color: #666;
}

.button-section {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-start {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.btn-start:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40,167,69,0.4);
}

.btn-restart {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
    color: white;
}

.btn-restart:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255,193,7,0.4);
}

.btn-stop {
    background: linear-gradient(45deg, #dc3545, #e83e8c);
    color: white;
}

.btn-stop:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(220,53,69,0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.output-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.output-section h3 {
    margin-bottom: 15px;
    color: #333;
}

.output-container {
    background: #1e1e1e;
    border-radius: 8px;
    padding: 15px;
    height: 400px;
    overflow-y: auto;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 14px;
    line-height: 1.4;
    border: 2px solid #333;
}

#output-content {
    color: #f8f8f2;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.output-controls {
    margin-top: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.output-controls label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.info-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.info-section h3 {
    margin-bottom: 15px;
    color: #333;
}

.info-section ul {
    list-style-type: none;
    padding-left: 0;
}

.info-section li {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.info-section li:last-child {
    border-bottom: none;
}

.info-section code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', monospace;
    color: #e83e8c;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .button-section {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 200px;
    }
    
    .status-section {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}
