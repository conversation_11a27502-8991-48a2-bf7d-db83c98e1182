#!/usr/bin/env python3
"""
启动脚本 - AI视频编辑器进程管理器
"""

import subprocess
import sys
import os

def check_dependencies():
    """检查依赖是否已安装"""
    try:
        import fastapi
        import uvicorn
        import jinja2
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def main():
    print("🚀 AI视频编辑器进程管理器")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 启动FastAPI应用
    print("🌐 启动Web服务器...")
    print("📍 访问地址: http://localhost:8000")
    print("⏹️  按 Ctrl+C 停止服务")
    print("=" * 50)
    
    try:
        # 使用uvicorn启动应用
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "app:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ])
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
